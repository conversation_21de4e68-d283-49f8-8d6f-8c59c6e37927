'use client';
import React, { useEffect } from 'react';
import Image from 'next/image';
import { Icon } from '@iconify/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import useDataFetch from '@/hooks/useDataFetch';
import SkinPreview from '../skin/SkinPreview';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';

const SharedDiarySection = () => {
  const { data, isLoading, error } = useDataFetch({
    queryKey: 'shared-entries',
    endPoint: 'diary/shared-entries?page=1&limit=10&sortBy=createdAt',
  });
  const router = useRouter();
  const queryClient = useQueryClient();

  // Like mutation
  const likeMutation = useMutation({
    mutationFn: async (entryId) => {
      return await api.post(`/diary/entries/${entryId}/like`);
    },
    onSuccess: () => {
      // Invalidate and refetch shared entries to get updated like status
      queryClient.invalidateQueries({ queryKey: ['shared-entries'] });
    },
    onError: (error) => {
      console.error('Error liking entry:', error);
    }
  });

  // Handle like functionality
  const handleLike = (entryId) => {
    likeMutation.mutate(entryId);
  };

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  // Handle navigation to diary details
  const handleViewDiary = (entryId) => {
    router.push(`/diary/shared/${entryId}`);
  };
  const sharedEntries = data?.items || [];
  console.log('Shared entries:', sharedEntries);
  return (
    <div className="max-w-7xl mx-auto  px-5 xl:px-0">
      <div className="mt-5 mb-2 w-full text-[40px] font-semibold text-[#432005]">
        Shared Diary
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {sharedEntries.map((entry, index) => (
          <div
            key={index}
            className="bg-white rounded-lg shadow-md overflow-hidden"
          >
            <SkinPreview
              key={index}
              skin={entry.skin.templateContent}
              contentData={{
                subject: entry.title,
                body: entry.content,
                date: entry.entryDate,
              }}
            />
            {/* User info section with profile design */}
            <div
              className="p-4 bg-gradient-to-br from-[#FFF8F0] to-[#F5E6D3] relative"
              style={{
                boxShadow: `
                  2px 2px 12px 0px #F5D1B066 inset,
                  -2px -2px 12px 0px #F5D1B066 inset
                `,
              }}
            >
              <div className="flex items-center justify-between">
                {/* Left side - Profile info */}
                <div className="flex items-center space-x-3">
                  {/* Avatar */}
                  <div className="w-12 h-102 rounded-full overflow-hidden bg-gray-200">
                    <Image
                      src={
                        entry.user?.profilePicture || '/api/placeholder/64/64'
                      }
                      alt={entry.user?.name || 'User'}
                      width={54}
                      height={54}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Name and likes */}
                  <div>
                    <div className="text-sm text-gray-600 mb-1">Name</div>
                    <div className="text-lg font-semibold text-[#432005] mb-2">
                      {entry.diary.user?.name || 'User Name'}
                    </div>
                  </div>
                </div>

                {/* Right side - View Diary button */}
                <div className="">
                  {/* Likes section */}
                  <div
                    className="flex items-center space-x-1 cursor-pointer"
                    onClick={() => handleLike(entry.id)}
                  >
                    <Icon
                      icon="mdi:thumb-up"
                      className={`text-2xl transition-colors duration-200 ${
                        entry.hasLiked
                          ? 'text-yellow-500 border border-yellow-500 bg-white rounded-full p-1'
                          : 'text-[#432005]'
                      }`}
                    />
                    <span className="text-xl font-bold text-[#432005]">
                      {entry.likeCount}
                    </span>
                  </div>
                  <button
                    onClick={() => handleViewDiary(entry.id)}
                    className="bg-gradient-to-br from-[#FFF8E1] to-[#F5E6A3] px-6 py-2 rounded-full font-semibold text-[#432005] border border-[#E6D16A] hover:shadow-lg transition-all duration-200 flex items-center space-x-2"
                    style={{
                      boxShadow: `
                      2px 2px 8px 0px rgba(0,0,0,0.1),
                      inset 1px 1px 3px 0px rgba(255,255,255,0.8)
                    `,
                    }}
                  >
                    <span>View Diary</span>
                    <span className="text-lg">😊</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SharedDiarySection;
